/* C2 Dashboard Theme - Black/Dark Gray Background, Red Icons/Buttons, White Text */

:root {
    --c2-red: #dc3545;
    --c2-red-dark: #b02a37;
    --c2-red-light: #ff6b7a;
    --c2-red-bright: #ff4757;
    --c2-black: #000000;
    --c2-dark-gray: #0d0d0d;
    --c2-medium-gray: #1a1a1a;
    --c2-light-gray: #2d2d2d;
    --c2-border-gray: #333333;
    --c2-white: #ffffff;
    --c2-off-white: #f8f9fa;
    --c2-text-muted: #cccccc;
}

/* Global Styles */
body {
    background-color: var(--c2-black) !important;
    color: var(--c2-white) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Custom Bootstrap Overrides */
.bg-dark {
    background-color: var(--c2-dark-gray) !important;
}

.bg-black {
    background-color: var(--c2-black) !important;
}

.border-danger {
    border-color: var(--c2-red) !important;
}

.border-secondary {
    border-color: var(--c2-border-gray) !important;
}

.text-danger {
    color: var(--c2-red) !important;
}

.text-white {
    color: var(--c2-white) !important;
}

.text-muted {
    color: var(--c2-text-muted) !important;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

.status-indicator.status-online {
    background-color: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.status-indicator.status-offline {
    background-color: var(--c2-red);
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.status-indicator.status-starting {
    background-color: #ffc107;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.status-indicator.status-stopping {
    background-color: #fd7e14;
    box-shadow: 0 0 10px rgba(253, 126, 20, 0.5);
}

/* Client List Styles */
.clients-container {
    max-height: 400px;
    overflow-y: auto;
    background-color: var(--c2-dark-gray);
}

.client-item {
    background-color: var(--c2-medium-gray);
    border: 1px solid var(--c2-border-gray);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.client-item:hover {
    background-color: var(--c2-light-gray);
    border-color: var(--c2-red);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.client-item.selected {
    background-color: var(--c2-red-dark);
    border-color: var(--c2-red-bright);
    box-shadow: 0 0 15px rgba(220, 53, 69, 0.5);
}

.client-id {
    font-weight: bold;
    color: var(--c2-white);
    margin-bottom: 4px;
}

.client-address {
    font-size: 0.9em;
    color: var(--c2-text-muted);
    margin-bottom: 2px;
}

/* Icons in client items should be red */
.client-item .bi {
    color: var(--c2-red) !important;
}

/* Output Area */
.output-area {
    background-color: var(--c2-black);
    color: var(--c2-white);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    padding: 20px;
    height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    border-radius: 0 0 8px 8px;
    scrollbar-width: thin;
    scrollbar-color: var(--c2-red) var(--c2-dark-gray);
    border: 1px solid var(--c2-border-gray);
}

.output-area::-webkit-scrollbar {
    width: 8px;
}

.output-area::-webkit-scrollbar-track {
    background: var(--c2-dark-gray);
}

.output-area::-webkit-scrollbar-thumb {
    background: var(--c2-red);
    border-radius: 4px;
}

.output-area::-webkit-scrollbar-thumb:hover {
    background: var(--c2-red-bright);
}

/* Form Controls */
.form-control {
    background-color: var(--c2-dark-gray) !important;
    border-color: var(--c2-border-gray) !important;
    color: var(--c2-white) !important;
}

.form-control:focus {
    background-color: var(--c2-dark-gray) !important;
    border-color: var(--c2-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    color: var(--c2-white) !important;
}

.form-control::placeholder {
    color: var(--c2-text-muted) !important;
}

.input-group-text {
    background-color: var(--c2-black) !important;
    border-color: var(--c2-border-gray) !important;
    color: var(--c2-red) !important;
}

/* Button Styles */
.btn-danger {
    background-color: var(--c2-red) !important;
    border-color: var(--c2-red) !important;
    color: var(--c2-white) !important;
}

.btn-danger:hover {
    background-color: var(--c2-red-bright) !important;
    border-color: var(--c2-red-bright) !important;
    color: var(--c2-white) !important;
}

.btn-outline-danger {
    color: var(--c2-red) !important;
    border-color: var(--c2-red) !important;
    background-color: transparent !important;
}

.btn-outline-danger:hover {
    background-color: var(--c2-red) !important;
    border-color: var(--c2-red) !important;
    color: var(--c2-white) !important;
}

.btn-success {
    background-color: var(--c2-red) !important;
    border-color: var(--c2-red) !important;
    color: var(--c2-white) !important;
}

.btn-success:hover {
    background-color: var(--c2-red-bright) !important;
    border-color: var(--c2-red-bright) !important;
    color: var(--c2-white) !important;
}

.btn-outline-light {
    color: var(--c2-white) !important;
    border-color: var(--c2-border-gray) !important;
    background-color: transparent !important;
}

.btn-outline-light:hover {
    background-color: var(--c2-red) !important;
    border-color: var(--c2-red) !important;
    color: var(--c2-white) !important;
}

/* Card Styles */
.card {
    background-color: var(--c2-dark-gray) !important;
    border-color: var(--c2-border-gray) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.5);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.6);
}

.card-header {
    background-color: var(--c2-black) !important;
    border-bottom: 1px solid var(--c2-red) !important;
    color: var(--c2-white) !important;
}

.card-body {
    background-color: var(--c2-dark-gray) !important;
    color: var(--c2-white) !important;
}

.card-title {
    color: var(--c2-white) !important;
}

/* Alert Styles */
.alert-dark {
    background-color: var(--c2-medium-gray) !important;
    border-color: var(--c2-border-gray) !important;
    color: var(--c2-white) !important;
}

/* Navbar */
.navbar {
    background-color: var(--c2-black) !important;
    border-bottom: 1px solid var(--c2-red) !important;
}

.navbar-dark .navbar-brand {
    color: var(--c2-white) !important;
    font-weight: bold;
    font-size: 1.5rem;
}

/* All icons should be red by default */
.bi {
    color: var(--c2-red) !important;
}

/* Text colors */
.text-success {
    color: var(--c2-red) !important;
}

.text-info {
    color: var(--c2-red) !important;
}

.text-warning {
    color: #ffc107 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.5rem;
    }
    
    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
    
    .output-area {
        height: 300px;
        font-size: 12px;
    }
    
    .clients-container {
        max-height: 300px;
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Utility Classes */
.text-muted {
    color: var(--c2-text-muted) !important;
}

.border-0 {
    border: none !important;
}

/* Focus States */
*:focus {
    outline: 2px solid var(--c2-red) !important;
    outline-offset: 2px;
}

/* Selection */
::selection {
    background-color: var(--c2-red);
    color: var(--c2-white);
}

::-moz-selection {
    background-color: var(--c2-red);
    color: var(--c2-white);
}

/* Container and main background */
.container-fluid {
    background-color: var(--c2-black) !important;
}

/* Spinner colors */
.spinner-border {
    color: var(--c2-red) !important;
}

.spinner-border-sm {
    color: var(--c2-red) !important;
}

/* Button group styling */
.btn-group .btn {
    border-color: var(--c2-border-gray) !important;
}

/* Status indicator specific colors */
.status-indicator.status-online {
    background-color: var(--c2-red) !important;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

.status-indicator.status-offline {
    background-color: #666666 !important;
    box-shadow: 0 0 10px rgba(102, 102, 102, 0.5);
}

.status-indicator.status-starting {
    background-color: #ffc107 !important;
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
}

.status-indicator.status-stopping {
    background-color: #fd7e14 !important;
    box-shadow: 0 0 10px rgba(253, 126, 20, 0.5);
}
