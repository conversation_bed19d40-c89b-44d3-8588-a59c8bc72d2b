/* C2 Dashboard Animations and Effects */

/* Keyframe Animations */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(220, 53, 69, 0.6);
    }
    100% {
        box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes flashOverlay {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    10% {
        opacity: 1;
        transform: scale(1.05);
    }
    90% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.95);
    }
}

@keyframes imageZoom {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

.shake-effect {
    animation: shake 0.5s ease-in-out;
}

.bounce-effect {
    animation: bounce 1s ease-in-out;
}

.spin-effect {
    animation: spin 1s linear infinite;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Button Animations */
.btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:active {
    transform: scale(0.98);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Status Transitions */
.status-transition {
    transition: all 0.5s ease;
}

/* Client Item Animations */
.client-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.client-item:hover {
    transform: translateY(-2px) scale(1.02);
}

.client-item.selected {
    animation: glow 2s ease-in-out infinite;
}

/* Output Area Effects */
.output-area {
    position: relative;
}

.output-area.typing::after {
    content: '|';
    animation: blink 1s infinite;
    color: #dc3545;
}

/* Card Animations */
.card {
    animation: fadeIn 0.6s ease-out;
}

.card:nth-child(1) {
    animation-delay: 0.1s;
}

.card:nth-child(2) {
    animation-delay: 0.2s;
}

.card:nth-child(3) {
    animation-delay: 0.3s;
}

/* Navbar Animation */
.navbar {
    animation: slideInUp 0.8s ease-out;
}

/* Form Focus Effects */
.form-control {
    transition: all 0.3s ease;
}

.form-control:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25), 0 0 15px rgba(220, 53, 69, 0.3);
}

/* Progress Indicators */
.progress-bar {
    background: linear-gradient(45deg, #dc3545, #b02a37);
    animation: pulse 2s ease-in-out infinite;
}

/* Notification Effects */
.notification {
    animation: slideInRight 0.5s ease-out;
}

.notification.success {
    border-left: 4px solid #28a745;
}

.notification.error {
    border-left: 4px solid #dc3545;
    animation: shake 0.5s ease-in-out;
}

.notification.warning {
    border-left: 4px solid #ffc107;
}

/* Responsive Animation Adjustments */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (max-width: 768px) {
    .hover-lift:hover {
        transform: none;
    }
    
    .hover-scale:hover {
        transform: none;
    }
    
    .client-item:hover {
        transform: none;
    }
}

/* Utility Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* Connection Overlay Styles */
.connection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.connection-overlay.show {
    display: flex;
    animation: flashOverlay 2s ease-in-out;
}

.overlay-content {
    text-align: center;
    max-width: 600px;
    padding: 2rem;
}

.connection-image {
    max-width: 400px;
    max-height: 300px;
    width: 100%;
    height: auto;
    border-radius: 15px;
    border: 3px solid #dc3545;
    box-shadow: 0 0 30px rgba(220, 53, 69, 0.6);
    margin-bottom: 1rem;
    animation: imageZoom 2s ease-in-out;
}

.overlay-text h2 {
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    margin-bottom: 0.5rem;
    animation: pulse 2s ease-in-out;
}

.overlay-text p {
    font-size: 1.2rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    animation: fadeIn 2s ease-in-out;
}

@media (max-width: 768px) {
    .connection-image {
        max-width: 300px;
        max-height: 200px;
    }

    .overlay-text h2 {
        font-size: 1.8rem;
    }

    .overlay-text p {
        font-size: 1rem;
    }
}
