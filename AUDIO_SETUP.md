# C2 Server Audio System Setup

This guide will help you set up the audio system for your C2 server that plays MP3 files when clients connect.

## Features

- **Automatic Playback**: Plays MP3 for 5 seconds when a client connects
- **State Persistence**: Remembers playback position between connections
- **Continuous Playback**: Resumes from where it left off on next connection
- **Auto-Loop**: Loops the audio when it reaches the end
- **Web Interface**: Control audio through the web dashboard
- **Background Operation**: Runs in background, pauses when no connections

## Installation

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

This will install:
- `pygame` - For audio playback
- `requests` - For downloading sample audio files
- `Flask` - Web framework (already required)

### 2. Setup Audio File

#### Option A: Use Setup Script (Recommended)
```bash
python setup_audio.py
```

This script will:
- Let you choose from sample audio files
- Download and set up the audio automatically
- Allow you to use your own MP3 file

#### Option B: Manual Setup
1. Place your MP3 file in the project directory
2. Rename it to `connection_sound.mp3`

```bash
# Example: Copy your MP3 file
cp /path/to/your/audio.mp3 connection_sound.mp3
```

## Usage

### 1. Start the Server

```bash
python server.py
```

The audio system will initialize automatically when the server starts.

### 2. Audio Behavior

- **Client Connects**: Audio plays for 5 seconds from the current position
- **Client Disconnects**: Audio pauses and position is saved
- **Next Connection**: Audio resumes from saved position
- **End of File**: Audio loops back to the beginning
- **State Persistence**: Position is saved to `audio_state.json`

### 3. Web Interface Controls

Access the web interface at `http://localhost:1488` and use the audio controls:

- **Test Audio**: Manually trigger 5-second playback
- **Reset Position**: Reset audio to the beginning
- **Refresh**: Update audio status display
- **Progress Bar**: Shows current position in the audio file

## Configuration

### Audio Settings

You can modify these settings in `audio_manager.py`:

```python
# Play duration per connection (seconds)
self.play_duration = 5.0

# Audio file path
self.audio_file_path = "connection_sound.mp3"

# State file path
self.state_file = "audio_state.json"
```

### Changing Audio File

#### Via Code:
```python
from audio_manager import get_audio_manager

audio_manager = get_audio_manager()
audio_manager.set_audio_file("new_audio.mp3")
```

#### Via File System:
1. Replace `connection_sound.mp3` with your new file
2. Restart the server

## Troubleshooting

### Audio Not Playing

1. **Check Dependencies**:
   ```bash
   pip install pygame
   ```

2. **Check Audio File**:
   - Ensure `connection_sound.mp3` exists
   - Verify it's a valid MP3 file
   - Check file permissions

3. **Check System Audio**:
   - Ensure system audio is working
   - Check volume levels
   - Test with other audio applications

### Permission Issues

On Linux/Mac, you might need audio permissions:
```bash
# Add user to audio group (Linux)
sudo usermod -a -G audio $USER

# Restart session or reboot
```

### File Format Issues

- Only MP3 files are supported
- Ensure the file is not corrupted
- Try converting with ffmpeg if needed:
  ```bash
  ffmpeg -i input.wav -codec:a mp3 output.mp3
  ```

## API Endpoints

The audio system provides these API endpoints:

- `GET /api/audio/status` - Get current audio status
- `POST /api/audio/test` - Trigger test playback
- `POST /api/audio/reset` - Reset audio position

### Example API Usage

```bash
# Get audio status
curl http://localhost:1488/api/audio/status

# Test audio
curl -X POST http://localhost:1488/api/audio/test

# Reset position
curl -X POST http://localhost:1488/api/audio/reset
```

## Files Created

The audio system creates these files:

- `audio_state.json` - Stores current playback position
- `connection_sound.mp3` - The audio file to play
- `audio_manager.py` - Audio management code

## Advanced Usage

### Custom Audio Manager

You can extend the AudioManager class for custom behavior:

```python
from audio_manager import AudioManager

class CustomAudioManager(AudioManager):
    def __init__(self):
        super().__init__()
        self.play_duration = 10.0  # Play for 10 seconds
    
    def play_on_connection(self):
        # Custom connection logic
        print("Custom audio behavior")
        super().play_on_connection()
```

### Multiple Audio Files

To use different audio files for different events, modify the server code:

```python
# In server.py handle_connection function
if client_count == 1:
    audio_manager.set_audio_file("first_connection.mp3")
else:
    audio_manager.set_audio_file("connection_sound.mp3")

audio_manager.play_on_connection()
```

## Support

If you encounter issues:

1. Check the console output for error messages
2. Verify all dependencies are installed
3. Ensure the audio file exists and is valid
4. Test system audio with other applications
5. Check file permissions

The audio system is designed to fail gracefully - if audio cannot be played, the C2 server will continue to function normally without audio.
